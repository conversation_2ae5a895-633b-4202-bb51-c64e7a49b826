import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import { ChakraProvider, Box, Button, HStack, Text } from '@chakra-ui/react'
import {
  Outlet,
  RouterProvider,
  createRootRoute,
  createRoute,
  create<PERSON>out<PERSON>,
  <PERSON>,
} from '@tanstack/react-router'

import { system } from './theme.ts'
import './styles.css'
import reportWebVitals from './reportWebVitals.ts'
import { AuthProvider, useAuth } from './context/AuthContext.jsx'
import { LoginForm } from './components/LoginForm.jsx'
import { ProtectedRoute } from './components/ProtectedRoute.jsx'
import App from './App.tsx'

// Navigation component that safely uses useAuth hook
function Navigation() {
  const { user, loading, signOut } = useAuth()

  if (loading) {
    return (
      <Box as="header" bg="blue.500" color="white" p={4}>
        <Text>Loading...</Text>
      </Box>
    )
  }

  return (
    <Box as="header" bg="blue.500" color="white" p={4}>
      <HStack justify="space-between">
        <Link to="/" style={{ color: 'white', textDecoration: 'none', fontWeight: 'bold' }}>
          Interior Designer
        </Link>
        <HStack gap={4}>
          {user ? (
            <>
              <Link to="/dashboard" style={{ color: 'white', textDecoration: 'none' }}>
                Dashboard
              </Link>
              <Text fontSize="sm">Welcome, {user.email}</Text>
              <Button
                size="sm"
                colorScheme="whiteAlpha"
                variant="outline"
                onClick={() => signOut()}
              >
                Sign Out
              </Button>
            </>
          ) : (
            <>
              <Link to="/login" style={{ color: 'white', textDecoration: 'none' }}>
                <Button size="sm" colorScheme="whiteAlpha" variant="outline">
                  Login
                </Button>
              </Link>
            </>
          )}
        </HStack>
      </HStack>
    </Box>
  )
}

const rootRoute = createRootRoute({
  component: RootComponent,
})

function RootComponent() {
  return (
    <>
      <Navigation />
      <Outlet />
    </>
  )
}



const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: IndexPage,
})

const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: LoginPage,
})

const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/dashboard',
  component: DashboardPage,
})

function IndexPage() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Loading...</Text>
      </Box>
    )
  }

  // If user is authenticated, show the main app
  if (user) {
    return <App />
  }

  // If user is not authenticated, show welcome message
  return (
    <Box p={8} textAlign="center">
      <Text fontSize="2xl" fontWeight="bold" mb={4}>
        Welcome to Interior Designer
      </Text>
      <Text color="gray.600">
        Please log in to access your dashboard and start designing your space.
      </Text>
    </Box>
  )
}

function LoginPage() {
  return (
    <Box minH="calc(100vh - 80px)" display="flex" alignItems="center" justifyContent="center" bg="gray.50">
      <Box maxW="md" w="full" p={8}>
        <Text fontSize="3xl" fontWeight="bold" textAlign="center" mb={8}>
          Sign in to your account
        </Text>
        <LoginForm />
      </Box>
    </Box>
  )
}

function DashboardPage() {
  return (
    <ProtectedRoute>
      <Box p={8}>
        <h1>Dashboard</h1>
        <p>Welcome to your dashboard!</p>
      </Box>
    </ProtectedRoute>
  )
}

const routeTree = rootRoute.addChildren([indexRoute, loginRoute, dashboardRoute])

const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPreloadStaleTime: 0,
})

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('app')
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <ChakraProvider value={system}>
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
      </ChakraProvider>
    </StrictMode>,
  )
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals()
